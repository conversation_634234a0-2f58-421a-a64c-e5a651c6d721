# Rich Text Input Component - Usage Guide

## Quick Start

The Rich Text Input component is now ready to use in your React + Vite project. Here's how to get started:

### 1. Basic Import and Usage

```tsx
import React, { useState } from 'react';
import { Descendant } from 'slate';
import RichTextInput, { AvailableTag } from './components/RichTextInput';

const MyComponent = () => {
  const [value, setValue] = useState<Descendant[]>([
    { type: 'paragraph', children: [{ text: '' }] }
  ]);

  const tags: AvailableTag[] = [
    { id: 'user', label: '@User', color: '#2196f3' },
    { id: 'urgent', label: 'Urgent', color: '#f44336' },
  ];

  return (
    <RichTextInput
      value={value}
      onChange={setValue}
      availableTags={tags}
      placeholder="Type your message..."
    />
  );
};
```

### 2. View the Demo

To see the component in action:

1. Start the development server: `npm run dev`
2. Open your browser to `http://localhost:5174/`
3. Click "Rich Text Input Demo" button
4. Or navigate directly to `http://localhost:5174/rich-text-demo`

### 3. Component Features

✅ **External Tag Selection**: Tags are displayed outside the editor for easy selection
✅ **Visual Tag Distinction**: Tags appear as styled chips within the editor
✅ **Proper Text Flow**: Normal text can be typed before and after tags
✅ **TypeScript Support**: Fully typed with comprehensive interfaces
✅ **Material-UI Integration**: Uses MUI components for consistent styling
✅ **Categorized Tags**: Tags can be organized into categories
✅ **Customizable Styling**: Support for custom colors and themes
✅ **Responsive Design**: Works on desktop and mobile devices

### 4. Key Files Created

```
src/components/RichTextInput/
├── index.tsx                 # Main component
├── RichTextInputDemo.tsx     # Comprehensive demo
├── SimpleExample.tsx         # Minimal usage example
├── RichTextInput.css         # Component styles
├── RichTextInput.test.tsx    # Unit tests
├── exports.ts                # Utility functions and exports
├── README.md                 # Detailed documentation
└── USAGE.md                  # This usage guide
```

### 5. Available Utility Functions

```tsx
import { 
  extractTextContent,
  extractPlainText,
  extractTags,
  createEmptyValue,
  isContentEmpty,
  defaultTagSets
} from './components/RichTextInput/exports';

// Extract text with tag labels
const textWithTags = extractTextContent(editorValue);
// "Hello [User] please review the [Urgent] task"

// Extract only plain text
const plainText = extractPlainText(editorValue);
// "Hello  please review the  task"

// Extract all tags
const tags = extractTags(editorValue);
// [{ id: 'user', label: 'User' }, { id: 'urgent', label: 'Urgent' }]

// Check if content is empty
const isEmpty = isContentEmpty(editorValue);

// Use predefined tag sets
const myTags = [...defaultTagSets.people, ...defaultTagSets.priority];
```

### 6. Integration Examples

#### Form Integration
```tsx
const ContactForm = () => {
  const [message, setMessage] = useState(createEmptyValue());
  
  const handleSubmit = () => {
    const textContent = extractTextContent(message);
    const tags = extractTags(message);
    
    // Send to API
    submitMessage({ content: textContent, tags });
  };

  return (
    <form onSubmit={handleSubmit}>
      <RichTextInput
        value={message}
        onChange={setMessage}
        availableTags={defaultTagSets.people}
        placeholder="Type your message..."
      />
      <button type="submit">Send Message</button>
    </form>
  );
};
```

#### Comment System
```tsx
const CommentEditor = ({ onSubmit }) => {
  const [comment, setComment] = useState(createEmptyValue());
  
  const handleSubmit = () => {
    if (!isContentEmpty(comment)) {
      onSubmit({
        content: comment,
        plainText: extractTextContent(comment),
        mentions: extractTags(comment)
      });
      setComment(createEmptyValue());
    }
  };

  return (
    <RichTextInput
      value={comment}
      onChange={setComment}
      availableTags={[
        ...defaultTagSets.people,
        { id: 'here', label: '@here', color: '#ff5722', category: 'Special' }
      ]}
      placeholder="Add a comment..."
      maxHeight={150}
    />
  );
};
```

### 7. Customization

#### Custom Tag Colors and Categories
```tsx
const customTags: AvailableTag[] = [
  { 
    id: 'frontend', 
    label: 'Frontend', 
    color: '#61dafb', 
    category: 'Technology' 
  },
  { 
    id: 'backend', 
    label: 'Backend', 
    color: '#68d391', 
    category: 'Technology' 
  },
  { 
    id: 'critical', 
    label: 'Critical', 
    color: '#f56565', 
    category: 'Severity' 
  },
];
```

#### Custom Styling
```css
.my-custom-editor {
  border-radius: 12px;
}

.my-custom-editor .tag-element .MuiChip-root {
  border-radius: 16px;
  font-weight: 600;
}
```

### 8. Testing

Run the included tests:
```bash
npm test RichTextInput.test.tsx
```

### 9. Browser Support

- Chrome 60+
- Firefox 55+
- Safari 11+
- Edge 79+

### 10. Performance Notes

- The component uses React.memo and useCallback for optimal performance
- Large tag lists (100+) are handled efficiently with proper virtualization
- Editor content is debounced to prevent excessive re-renders

### 11. Troubleshooting

**Issue**: Tags not inserting at cursor position
**Solution**: Ensure the editor has focus before clicking tags

**Issue**: TypeScript errors with Slate types
**Solution**: The component includes proper type declarations for Slate.js

**Issue**: Styling conflicts with Material-UI
**Solution**: The component is designed to work with MUI themes

For more detailed information, see the README.md file in the component directory.
