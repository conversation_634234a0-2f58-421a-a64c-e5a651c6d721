import React, { useState } from 'react';
import { Box, Chip, Paper, Typography, Stack, Button } from '@mui/material';
import { RichTextInput, AvailableTag } from './index';
import { Descendant } from 'slate';

// 示例可用标签
const availableTags: AvailableTag[] = [
  { id: 'user', label: 'User', color: '#2196f3', category: 'People' },
  { id: 'project', label: 'Project', color: '#4caf50', category: 'Work' },
  { id: 'urgent', label: 'Urgent', color: '#f44336', category: 'Priority' },
  { id: 'meeting', label: 'Meeting', color: '#ff9800', category: 'Events' },
  { id: 'deadline', label: 'Deadline', color: '#9c27b0', category: 'Time' },
  { id: 'review', label: 'Review', color: '#607d8b', category: 'Process' },
];

export const RichTextInputExample: React.FC = () => {
  const [selectedTags, setSelectedTags] = useState<AvailableTag[]>([]);
  const [editorValue, setEditorValue] = useState<Descendant[]>();
  const [plainText, setPlainText] = useState<string>('');

  // 处理标签选择
  const handleTagClick = (tag: AvailableTag) => {
    setSelectedTags((prev) => {
      const isSelected = prev.some((t) => t.id === tag.id);
      if (isSelected) {
        return prev.filter((t) => t.id !== tag.id);
      } else {
        return [...prev, tag];
      }
    });
  };

  // 清空所有标签
  const clearTags = () => {
    setSelectedTags([]);
  };

  // 按类别分组标签
  const groupedTags = React.useMemo(() => {
    const groups: Record<string, AvailableTag[]> = {};
    availableTags.forEach((tag) => {
      const category = tag.category || 'Other';
      if (!groups[category]) {
        groups[category] = [];
      }
      groups[category].push(tag);
    });
    return groups;
  }, []);

  return (
    <Box sx={{ p: 3, maxWidth: 800, mx: 'auto' }}>
      <Typography variant="h5" gutterBottom>
        Rich Text Input 示例
      </Typography>

      {/* 标签选择区域 */}
      <Paper
        elevation={1}
        sx={{
          p: 2,
          mb: 2,
          backgroundColor: '#fafafa',
          border: '1px solid #e0e0e0',
        }}
      >
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="subtitle2">可用标签</Typography>
          <Button size="small" onClick={clearTags} disabled={selectedTags.length === 0}>
            清空选择
          </Button>
        </Box>

        <Stack spacing={2}>
          {Object.entries(groupedTags).map(([category, tags]) => (
            <Box key={category}>
              <Typography variant="caption" color="text.secondary" sx={{ mb: 1, display: 'block' }}>
                {category}
              </Typography>
              <Stack direction="row" spacing={1} flexWrap="wrap" useFlexGap>
                {tags.map((tag) => {
                  const isSelected = selectedTags.some((t) => t.id === tag.id);
                  return (
                    <Chip
                      key={tag.id}
                      label={tag.label}
                      onClick={() => handleTagClick(tag)}
                      size="small"
                      variant={isSelected ? 'filled' : 'outlined'}
                      sx={{
                        cursor: 'pointer',
                        backgroundColor: isSelected ? tag.color : tag.color ? `${tag.color}15` : undefined,
                        borderColor: tag.color || undefined,
                        color: isSelected ? 'white' : tag.color || undefined,
                        '&:hover': {
                          backgroundColor: isSelected ? tag.color : tag.color ? `${tag.color}25` : undefined,
                        },
                      }}
                    />
                  );
                })}
              </Stack>
            </Box>
          ))}
        </Stack>

        {/* 已选择的标签 */}
        {selectedTags.length > 0 && (
          <Box sx={{ mt: 2, pt: 2, borderTop: '1px solid #e0e0e0' }}>
            <Typography variant="caption" color="text.secondary" sx={{ mb: 1, display: 'block' }}>
              已选择的标签:
            </Typography>
            <Stack direction="row" spacing={1} flexWrap="wrap" useFlexGap>
              {selectedTags.map((tag) => (
                <Chip
                  key={tag.id}
                  label={tag.label}
                  size="small"
                  variant="filled"
                  sx={{
                    backgroundColor: tag.color,
                    color: 'white',
                  }}
                />
              ))}
            </Stack>
          </Box>
        )}
      </Paper>

      {/* 富文本输入框 */}
      <RichTextInput
        value={editorValue}
        onChange={setEditorValue}
        selectedTags={selectedTags}
        onTextChange={setPlainText}
        placeholder="输入您的消息..."
        maxHeight={200}
      />

      {/* 输出信息 */}
      <Paper
        elevation={1}
        sx={{
          p: 2,
          mt: 2,
          backgroundColor: '#f5f5f5',
          border: '1px solid #e0e0e0',
        }}
      >
        <Typography variant="subtitle2" gutterBottom>
          输出信息:
        </Typography>

        <Box sx={{ mb: 2 }}>
          <Typography variant="caption" color="text.secondary">
            选中的标签:
          </Typography>
          <Typography variant="body2" sx={{ mt: 0.5, p: 1, backgroundColor: 'white', borderRadius: 1 }}>
            {selectedTags.length > 0 ? selectedTags.map((tag) => `@${tag.label}`).join(' ') : '(无)'}
          </Typography>
        </Box>

        <Box sx={{ mb: 2 }}>
          <Typography variant="caption" color="text.secondary">
            纯文本内容:
          </Typography>
          <Typography variant="body2" sx={{ mt: 0.5, p: 1, backgroundColor: 'white', borderRadius: 1 }}>
            {plainText || '(空)'}
          </Typography>
        </Box>

        <Box>
          <Typography variant="caption" color="text.secondary">
            Slate 编辑器值:
          </Typography>
          <Typography
            variant="body2"
            component="pre"
            sx={{
              mt: 0.5,
              p: 1,
              backgroundColor: 'white',
              borderRadius: 1,
              fontSize: '12px',
              overflow: 'auto',
              maxHeight: '200px',
            }}
          >
            {JSON.stringify(editorValue, null, 2)}
          </Typography>
        </Box>
      </Paper>
    </Box>
  );
};

export default RichTextInputExample;

