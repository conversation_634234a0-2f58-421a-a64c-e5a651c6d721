import React, { useState, useCallback, useMemo } from 'react';
import {
  Box,
  Paper,
  Typography,
  Stack,
  Chip,
  Button,
  Divider,
} from '@mui/material';
import { Descendant } from 'slate';
import { RichTextInput, AvailableTag } from '../RichTextInput';
import { ChatInputProps, TagGroup } from './types';
import SendIcon from '@mui/icons-material/Send';

// 默认可用标签
const defaultAvailableTags: AvailableTag[] = [
  { id: 'user', label: 'User', color: '#2196f3', category: 'People' },
  { id: 'project', label: 'Project', color: '#4caf50', category: 'Work' },
  { id: 'urgent', label: 'Urgent', color: '#f44336', category: 'Priority' },
  { id: 'meeting', label: 'Meeting', color: '#ff9800', category: 'Events' },
  { id: 'deadline', label: 'Deadline', color: '#9c27b0', category: 'Time' },
  { id: 'review', label: 'Review', color: '#607d8b', category: 'Process' },
];

/**
 * ChatInput 组件 - RichTextInput 的重构版本
 * 将标签选择功能外部化，支持独立的标签和文本输入
 */
export const ChatInput: React.FC<ChatInputProps> = ({
  value,
  onChange,
  selectedTags = [],
  textValue = '',
  onTextChange,
  onTagsChange,
  availableTags = defaultAvailableTags,
  placeholder = 'Type your message...',
  disabled = false,
  maxHeight = 200,
  className = '',
  showTagSelection = true,
  tagSelectionTitle = 'Available Tags',
  showSelectedTagsPreview = true,
  onSend,
  showSendButton = false,
  sendButtonText = 'Send',
}) => {
  // 内部状态管理
  const [internalSelectedTags, setInternalSelectedTags] = useState<AvailableTag[]>(selectedTags);
  const [internalTextValue, setInternalTextValue] = useState<string>(textValue);
  const [internalEditorValue, setInternalEditorValue] = useState<Descendant[]>(value || []);

  // 使用外部传入的值或内部状态
  const currentSelectedTags = selectedTags.length > 0 ? selectedTags : internalSelectedTags;
  const currentTextValue = textValue || internalTextValue;

  // 按类别分组标签
  const groupedTags = useMemo(() => {
    const groups: TagGroup = {};
    availableTags.forEach((tag) => {
      const category = tag.category || 'Other';
      if (!groups[category]) {
        groups[category] = [];
      }
      groups[category].push(tag);
    });
    return groups;
  }, [availableTags]);

  // 处理标签点击
  const handleTagClick = useCallback(
    (tag: AvailableTag) => {
      const newSelectedTags = currentSelectedTags.some((t) => t.id === tag.id)
        ? currentSelectedTags.filter((t) => t.id !== tag.id)
        : [...currentSelectedTags, tag];

      setInternalSelectedTags(newSelectedTags);
      onTagsChange?.(newSelectedTags);
    },
    [currentSelectedTags, onTagsChange]
  );

  // 处理富文本编辑器变化
  const handleEditorChange = useCallback(
    (newValue: Descendant[]) => {
      setInternalEditorValue(newValue);
      onChange?.(newValue);
    },
    [onChange]
  );

  // 处理文本变化
  const handleTextChange = useCallback(
    (text: string) => {
      setInternalTextValue(text);
      onTextChange?.(text);
    },
    [onTextChange]
  );

  // 清空所有标签
  const clearTags = useCallback(() => {
    setInternalSelectedTags([]);
    onTagsChange?.([]);
  }, [onTagsChange]);

  // 处理发送
  const handleSend = useCallback(() => {
    if (onSend) {
      onSend({
        tags: currentSelectedTags,
        text: currentTextValue,
        richText: internalEditorValue,
      });
    }
  }, [onSend, currentSelectedTags, currentTextValue, internalEditorValue]);

  return (
    <Box className={`chat-input ${className}`}>
      {/* 标签选择区域 */}
      {showTagSelection && (
        <Paper elevation={1} sx={{ p: 2, mb: 2 }}>
          <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 2 }}>
            <Typography variant="h6" component="h3">
              {tagSelectionTitle}
            </Typography>
            {currentSelectedTags.length > 0 && (
              <Button variant="outlined" size="small" onClick={clearTags}>
                Clear All
              </Button>
            )}
          </Stack>

          <Stack spacing={2}>
            {Object.entries(groupedTags).map(([category, tags]) => (
              <Box key={category}>
                <Typography variant="caption" color="text.secondary" sx={{ mb: 1, display: 'block' }}>
                  {category}
                </Typography>
                <Stack direction="row" spacing={1} flexWrap="wrap" useFlexGap>
                  {tags.map((tag) => {
                    const isSelected = currentSelectedTags.some((t) => t.id === tag.id);
                    return (
                      <Chip
                        key={tag.id}
                        label={tag.label}
                        onClick={() => handleTagClick(tag)}
                        size="small"
                        variant={isSelected ? 'filled' : 'outlined'}
                        disabled={disabled}
                        sx={{
                          cursor: disabled ? 'default' : 'pointer',
                          backgroundColor: isSelected ? tag.color : tag.color ? `${tag.color}15` : undefined,
                          borderColor: tag.color || undefined,
                          color: isSelected ? 'white' : tag.color || undefined,
                          '&:hover': {
                            backgroundColor: disabled
                              ? undefined
                              : isSelected
                              ? tag.color
                              : tag.color
                              ? `${tag.color}25`
                              : undefined,
                          },
                        }}
                      />
                    );
                  })}
                </Stack>
              </Box>
            ))}
          </Stack>

          {/* 已选择标签预览 */}
          {showSelectedTagsPreview && currentSelectedTags.length > 0 && (
            <>
              <Divider sx={{ my: 2 }} />
              <Box>
                <Typography variant="caption" color="text.secondary" sx={{ mb: 1, display: 'block' }}>
                  Selected Tags:
                </Typography>
                <Stack direction="row" spacing={1} flexWrap="wrap" useFlexGap>
                  {currentSelectedTags.map((tag) => (
                    <Chip
                      key={tag.id}
                      label={tag.label}
                      size="small"
                      variant="filled"
                      sx={{
                        backgroundColor: tag.color,
                        color: 'white',
                      }}
                    />
                  ))}
                </Stack>
              </Box>
            </>
          )}
        </Paper>
      )}

      {/* 富文本输入区域 */}
      <Box sx={{ position: 'relative' }}>
        <RichTextInput
          value={internalEditorValue}
          onChange={handleEditorChange}
          selectedTags={currentSelectedTags}
          textValue={currentTextValue}
          onTextChange={handleTextChange}
          placeholder={placeholder}
          disabled={disabled}
          maxHeight={maxHeight}
        />

        {/* 发送按钮 */}
        {showSendButton && (
          <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end' }}>
            <Button
              variant="contained"
              endIcon={<SendIcon />}
              onClick={handleSend}
              disabled={disabled || (!currentTextValue.trim() && currentSelectedTags.length === 0)}
            >
              {sendButtonText}
            </Button>
          </Box>
        )}
      </Box>
    </Box>
  );
};

export default ChatInput;
