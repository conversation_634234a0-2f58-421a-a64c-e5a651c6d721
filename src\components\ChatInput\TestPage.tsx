import React from 'react';
import { Box, Typography, Container } from '@mui/material';
import { ChatInput } from './index';
import { AvailableTag } from './exports';

// 简单的测试标签
const testTags: AvailableTag[] = [
  { id: 'test1', label: 'Test 1', color: '#2196f3', category: 'Test' },
  { id: 'test2', label: 'Test 2', color: '#4caf50', category: 'Test' },
];

/**
 * 简单的测试页面，用于验证 ChatInput 组件是否正常工作
 */
export const TestPage: React.FC = () => {
  return (
    <Container maxWidth="md" sx={{ py: 4 }}>
      <Typography variant="h4" gutterBottom>
        ChatInput Test Page
      </Typography>
      
      <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
        This is a simple test page to verify that the ChatInput component works without errors.
      </Typography>

      <Box sx={{ mb: 4 }}>
        <Typography variant="h6" gutterBottom>
          Basic ChatInput
        </Typography>
        <ChatInput
          availableTags={testTags}
          placeholder="Type something here..."
          showSendButton={true}
          onSend={(content) => {
            console.log('Test send:', content);
            alert(`Sent: ${content.tags.length} tags, "${content.text}"`);
          }}
        />
      </Box>

      <Box sx={{ mb: 4 }}>
        <Typography variant="h6" gutterBottom>
          Text-Only ChatInput
        </Typography>
        <ChatInput
          showTagSelection={false}
          placeholder="Text only input..."
          onTextChange={(text) => console.log('Text changed:', text)}
        />
      </Box>
    </Container>
  );
};

export default TestPage;
