import React, { lazy } from 'react';
import type { RouteObject } from 'react-router-dom';
import { Navigate } from 'react-router-dom';

const Home = lazy(() => import('@/views/Home/Home'));
const Login = lazy(() => import('@/views/Login/Login'));
// const RichTextDemo = lazy(() => import('@/components/RichTextInput/RichTextInputExample'));
const RichTextInputExample = lazy(() => import('@/components/RichTextInput/RichTextInputExample'));
const ChatInputExample = lazy(() => import('@/components/ChatInput/ChatInputExample'));
const ChatInputTestPage = lazy(() => import('@/components/ChatInput/TestPage'));

export interface RouteProps {
  path?: string;
  name?: string;
  index?: boolean;
  icon?: React.ReactNode;
  children?: React.ReactNode;
  caseSensitive?: boolean;
  id?: string;
  element?: React.ReactNode | null;
  Component?: React.ComponentType | null;
  errorElement?: React.ReactNode | null;
  ErrorBoundary?: React.ComponentType | null;
  handle?: RouteObject['handle'];
}

const routers: Array<RouteProps> = [
  {
    path: '/',
    element: <Navigate to="/home" />,
  },
  {
    path: '/home',
    name: 'home',
    element: <Home />,
  },
  {
    path: '/login',
    name: 'login',
    element: <Login />,
  },
  // {
  //   path: '/rich-text-demo',
  //   name: 'rich-text-demo',
  //   element: <RichTextDemo />,
  // },
  {
    path: '/rich-text-input-example',
    name: 'rich-text-input-example',
    element: <RichTextInputExample />,
  },
  {
    path: '/chat-input-example',
    name: 'chat-input-example',
    element: <ChatInputExample />,
  },
  {
    path: '/chat-input-test',
    name: 'chat-input-test',
    element: <ChatInputTestPage />,
  },
];

export default routers;

