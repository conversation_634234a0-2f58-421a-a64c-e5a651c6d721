import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import RichTextInput, { AvailableTag } from './index';

// Mock Material-UI theme for testing
const theme = createTheme();

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ThemeProvider theme={theme}>{children}</ThemeProvider>
);

const mockTags: AvailableTag[] = [
  { id: 'user', label: 'User', color: '#2196f3', category: 'People' },
  { id: 'urgent', label: 'Urgent', color: '#f44336', category: 'Priority' },
  { id: 'meeting', label: 'Meeting', color: '#ff9800', category: 'Events' },
];

describe('RichTextInput', () => {
  it('renders without crashing', () => {
    render(
      <TestWrapper>
        <RichTextInput availableTags={mockTags} />
      </TestWrapper>
    );
    
    expect(screen.getByText('Available Tags')).toBeInTheDocument();
  });

  it('displays available tags grouped by category', () => {
    render(
      <TestWrapper>
        <RichTextInput availableTags={mockTags} />
      </TestWrapper>
    );
    
    expect(screen.getByText('People')).toBeInTheDocument();
    expect(screen.getByText('Priority')).toBeInTheDocument();
    expect(screen.getByText('Events')).toBeInTheDocument();
    expect(screen.getByText('User')).toBeInTheDocument();
    expect(screen.getByText('Urgent')).toBeInTheDocument();
    expect(screen.getByText('Meeting')).toBeInTheDocument();
  });

  it('displays placeholder text', () => {
    const placeholder = 'Custom placeholder text';
    render(
      <TestWrapper>
        <RichTextInput availableTags={mockTags} placeholder={placeholder} />
      </TestWrapper>
    );
    
    expect(screen.getByText(placeholder)).toBeInTheDocument();
  });

  it('handles disabled state', () => {
    render(
      <TestWrapper>
        <RichTextInput availableTags={mockTags} disabled={true} />
      </TestWrapper>
    );
    
    // Check if tag buttons are disabled
    const tagButtons = screen.getAllByRole('button');
    tagButtons.forEach(button => {
      expect(button).toBeDisabled();
    });
  });

  it('calls onChange when content changes', () => {
    const mockOnChange = jest.fn();
    render(
      <TestWrapper>
        <RichTextInput availableTags={mockTags} onChange={mockOnChange} />
      </TestWrapper>
    );
    
    // This test would require more complex setup to simulate Slate.js editor changes
    // For now, we just verify the component renders with the onChange prop
    expect(mockOnChange).toBeDefined();
  });

  it('applies custom className', () => {
    const customClass = 'custom-rich-text-input';
    const { container } = render(
      <TestWrapper>
        <RichTextInput availableTags={mockTags} className={customClass} />
      </TestWrapper>
    );
    
    expect(container.querySelector(`.${customClass}`)).toBeInTheDocument();
  });

  it('handles empty tags array', () => {
    render(
      <TestWrapper>
        <RichTextInput availableTags={[]} />
      </TestWrapper>
    );
    
    expect(screen.getByText('Available Tags')).toBeInTheDocument();
    // Should still render the component without errors
  });

  it('groups tags without category under "Other"', () => {
    const tagsWithoutCategory: AvailableTag[] = [
      { id: 'test', label: 'Test Tag' },
    ];
    
    render(
      <TestWrapper>
        <RichTextInput availableTags={tagsWithoutCategory} />
      </TestWrapper>
    );
    
    expect(screen.getByText('Other')).toBeInTheDocument();
    expect(screen.getByText('Test Tag')).toBeInTheDocument();
  });
});

// Integration test example
describe('RichTextInput Integration', () => {
  it('renders with initial value', () => {
    const initialValue = [
      {
        type: 'paragraph',
        children: [{ text: 'Initial content' }],
      },
    ];
    
    render(
      <TestWrapper>
        <RichTextInput availableTags={mockTags} value={initialValue} />
      </TestWrapper>
    );
    
    // The actual text content testing would require more complex Slate.js testing setup
    expect(screen.getByText('Available Tags')).toBeInTheDocument();
  });
});

// Type checking tests (these would be caught at compile time)
describe('RichTextInput TypeScript', () => {
  it('accepts correct prop types', () => {
    const validProps = {
      availableTags: mockTags,
      placeholder: 'Test placeholder',
      disabled: false,
      maxHeight: 300,
      className: 'test-class',
    };
    
    // This test ensures TypeScript compilation succeeds with valid props
    expect(() => {
      render(
        <TestWrapper>
          <RichTextInput {...validProps} />
        </TestWrapper>
      );
    }).not.toThrow();
  });
});
