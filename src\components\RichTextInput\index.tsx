import React, { useCallback, useMemo, useState } from 'react';
import { createEditor, Descendant, Editor, Element as SlateElement, Transforms, Range, Point } from 'slate';
import { Slate, Editable, withReact, RenderElementProps, RenderLeafProps, ReactEditor, useSlateStatic } from 'slate-react';
import { withHistory } from 'slate-history';
import { Box, Chip, Paper } from '@mui/material';
import './RichTextInput.css';

// Available tag interface
export interface AvailableTag {
  id: string;
  label: string;
  color?: string;
  category?: string;
}

// Define custom types for our editor
export interface TagElement {
  type: 'tag';
  tagId: string;
  label: string;
  children: [{ text: '' }];
}

export interface ParagraphElement {
  type: 'paragraph';
  children: Descendant[];
}

export interface TextElement {
  text: string;
}

export type CustomElement = TagElement | ParagraphElement;
export type CustomText = TextElement;

declare module 'slate' {
  interface CustomTypes {
    Element: CustomElement;
    Text: CustomText;
  }
}

// Default available tags
const defaultTags: AvailableTag[] = [
  { id: 'user', label: 'User', color: '#2196f3', category: 'People' },
  { id: 'project', label: 'Project', color: '#4caf50', category: 'Work' },
  { id: 'urgent', label: 'Urgent', color: '#f44336', category: 'Priority' },
  { id: 'meeting', label: 'Meeting', color: '#ff9800', category: 'Events' },
  { id: 'deadline', label: 'Deadline', color: '#9c27b0', category: 'Time' },
  { id: 'review', label: 'Review', color: '#607d8b', category: 'Process' },
];

// Component props interface
export interface RichTextInputProps {
  value?: Descendant[];
  onChange?: (value: Descendant[]) => void;
  selectedTags?: AvailableTag[]; // 外部传入的选中标签
  textValue?: string; // 外部传入的文本内容
  onTextChange?: (text: string) => void; // 纯文本变化回调
  placeholder?: string;
  disabled?: boolean;
  maxHeight?: number;
  className?: string;
}

// Helper function to check if a node is a tag element
const isTagElement = (element: any): element is TagElement => {
  return element.type === 'tag';
};

// Custom editor methods
const withTags = <T extends Editor>(editor: T): T => {
  const { isInline, isVoid } = editor;

  editor.isInline = (element: SlateElement) => {
    return isTagElement(element) ? true : isInline(element);
  };

  editor.isVoid = (element: SlateElement) => {
    return isTagElement(element) ? true : isVoid(element);
  };

  // Add custom method to editor
  (editor as any).insertTag = (tag: AvailableTag) => {
    const tagElement: TagElement = {
      type: 'tag',
      tagId: tag.id,
      label: tag.label,
      children: [{ text: '' }],
    };

    Transforms.insertNodes(editor, tagElement);
    Transforms.move(editor);
  };

  // Override deleteBackward and deleteForward to protect tags
  const { deleteBackward, deleteForward, insertBreak } = editor;

  editor.deleteBackward = (unit) => {
    const { selection } = editor;

    if (selection && Range.isCollapsed(selection)) {
      const [match] = Editor.nodes(editor, {
        match: (n) => !Editor.isEditor(n) && SlateElement.isElement(n) && n.type === 'tag',
        mode: 'highest',
      });

      if (match) {
        const [, path] = match;
        const start = Editor.start(editor, path);

        if (Point.equals(selection.anchor, start)) {
          return;
        }
      }
    }

    deleteBackward(unit);
  };

  editor.deleteForward = (unit) => {
    const { selection } = editor;

    if (selection && Range.isCollapsed(selection)) {
      const [match] = Editor.nodes(editor, {
        match: (n) => !Editor.isEditor(n) && SlateElement.isElement(n) && n.type === 'tag',
        mode: 'highest',
      });

      if (match) {
        const [, path] = match;
        const end = Editor.end(editor, path);

        if (Point.equals(selection.anchor, end)) {
          return;
        }
      }
    }

    deleteForward(unit);
  };

  return editor;
};

// Element renderer component
const renderElement = ({ attributes, children, element }: RenderElementProps) => {
  const editor = useSlateStatic();

  switch (element.type) {
    case 'tag':
      const tagElement = element as TagElement;
      return (
        <span
          {...attributes}
          contentEditable={false}
          className="tag-element"
          style={{
            display: 'inline-block',
            margin: '0 2px',
            verticalAlign: 'baseline',
            position: 'relative',
          }}
        >
          <Chip
            label={tagElement.label}
            size="small"
            variant="filled"
            onDelete={() => {
              // Find the path to this element and remove it
              try {
                const path = ReactEditor.findPath(editor as ReactEditor, element);
                Transforms.removeNodes(editor, { at: path });
              } catch (error) {
                // Fallback: remove by matching the element
                Transforms.removeNodes(editor, {
                  match: (n) => n === element,
                });
              }
            }}
            sx={{
              height: '20px',
              fontSize: '12px',
              backgroundColor: '#e3f2fd',
              color: '#1976d2',
              '& .MuiChip-label': {
                padding: '0 6px',
              },
              '& .MuiChip-deleteIcon': {
                fontSize: '14px',
                color: '#1976d2',
                '&:hover': {
                  color: '#d32f2f',
                },
              },
            }}
          />
          {children}
        </span>
      );
    case 'paragraph':
    default:
      return <div {...attributes}>{children}</div>;
  }
};

// Leaf renderer component
const renderLeaf = ({ attributes, children }: RenderLeafProps) => {
  return <span {...attributes}>{children}</span>;
};

// Helper function to extract plain text from Slate nodes
const extractText = (nodes: Descendant[]): string => {
  return nodes
    .map((node) => {
      if ('text' in node) {
        return node.text;
      } else if ('children' in node) {
        if (node.type === 'tag') {
          return `@${(node as TagElement).label}`;
        }
        return extractText(node.children);
      }
      return '';
    })
    .join('');
};

// Main RichTextInput component
export const RichTextInput: React.FC<RichTextInputProps> = ({
  value,
  onChange,
  selectedTags = [],
  textValue,
  onTextChange,
  placeholder = 'Type your message...',
  disabled = false,
  maxHeight = 200,
  className = '',
}) => {
  // Initialize editor with plugins
  const editor = useMemo(() => withTags(withHistory(withReact(createEditor()))), []);

  // Default value for the editor
  const initialValue: Descendant[] = [
    {
      type: 'paragraph',
      children: [{ text: '' }],
    },
  ];

  const [editorValue, setEditorValue] = useState<Descendant[]>(value || initialValue);

  // Handle editor value changes
  const handleChange = useCallback(
    (newValue: Descendant[]) => {
      setEditorValue(newValue);
      onChange?.(newValue);

      // Extract and send plain text
      if (onTextChange) {
        const plainText = extractText(newValue);
        onTextChange(plainText);
      }
    },
    [onChange, onTextChange]
  );

  // Insert selected tags when they change
  React.useEffect(() => {
    // Always clear the editor content first when selectedTags changes
    Transforms.delete(editor, {
      at: {
        anchor: Editor.start(editor, []),
        focus: Editor.end(editor, []),
      },
    });

    // Insert all selected tags if any
    if (selectedTags.length > 0) {
      selectedTags.forEach((tag, index) => {
        (editor as any).insertTag(tag);
        // Add space between tags except for the last one
        if (index < selectedTags.length - 1) {
          Transforms.insertText(editor, ' ');
        }
      });
    }
  }, [selectedTags, editor]);

  // Handle key events to protect tags and control line breaks
  const handleKeyDown = useCallback(
    (event: React.KeyboardEvent) => {
      const { selection } = editor;

      // Handle Enter key behavior: Shift+Enter for line break, Enter alone does nothing
      if (event.key === 'Enter') {
        if (event.shiftKey) {
          // Shift+Enter: Allow line break
          // Let the default behavior happen
          return;
        } else {
          // Enter alone: Prevent line break
          event.preventDefault();
          return;
        }
      }

      // Handle Ctrl+A followed by Delete/Backspace
      if (event.key === 'Delete' || event.key === 'Backspace') {
        if (selection && Range.isExpanded(selection)) {
          // Check if the selection spans the entire editor
          const editorRange = Editor.range(editor, []);
          if (Range.equals(selection, editorRange)) {
            event.preventDefault();

            // Only delete text nodes, preserve tags
            const tags: TagElement[] = [];
            const nodes = Array.from(
              Editor.nodes(editor, {
                match: (n) => !Editor.isEditor(n) && SlateElement.isElement(n) && n.type === 'tag',
              })
            );

            // Collect all tag elements
            nodes.forEach(([node]) => {
              if (SlateElement.isElement(node) && node.type === 'tag') {
                tags.push(node as TagElement);
              }
            });

            // Clear all content
            Transforms.delete(editor, {
              at: editorRange,
            });

            // Re-insert tags
            tags.forEach((tag, index) => {
              (editor as any).insertTag({
                id: tag.tagId,
                label: tag.label,
              });
              if (index < tags.length - 1) {
                Transforms.insertText(editor, ' ');
              }
            });

            return;
          }
        }
      }
    },
    [editor]
  );

  return (
    <Box className={`rich-text-input ${className}`}>
      {/* Editor Section */}
      <Paper
        elevation={1}
        sx={{
          border: '1px solid #e0e0e0',
          borderRadius: 1,
          overflow: 'hidden',
        }}
      >
        <Box
          sx={{
            p: 2,
            maxHeight: `${maxHeight}px`,
            overflowY: 'auto',
            minHeight: '100px',
            '&:focus-within': {
              borderColor: '#1976d2',
            },
          }}
        >
          <Slate editor={editor} initialValue={editorValue} onValueChange={handleChange}>
            <Editable
              placeholder={placeholder}
              disabled={disabled}
              renderElement={renderElement}
              renderLeaf={renderLeaf}
              onKeyDown={handleKeyDown}
              style={{
                outline: 'none',
                minHeight: '60px',
                lineHeight: '1.5',
                fontSize: '14px',
              }}
            />
          </Slate>
        </Box>
      </Paper>
    </Box>
  );
};

export default RichTextInput;

