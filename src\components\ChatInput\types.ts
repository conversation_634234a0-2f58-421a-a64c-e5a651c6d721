import { Descendant } from 'slate';
import { AvailableTag } from '../RichTextInput';

/**
 * ChatInput 组件的 Props 接口
 */
export interface ChatInputProps {
  /** 富文本编辑器的值 */
  value?: Descendant[];
  
  /** 富文本编辑器值变化回调 */
  onChange?: (value: Descendant[]) => void;
  
  /** 当前选中的标签数组 */
  selectedTags?: AvailableTag[];
  
  /** 当前的文本内容 */
  textValue?: string;
  
  /** 文本内容变化回调 */
  onTextChange?: (text: string) => void;
  
  /** 标签选择变化回调 */
  onTagsChange?: (tags: AvailableTag[]) => void;
  
  /** 可用的标签列表 */
  availableTags?: AvailableTag[];
  
  /** 输入框占位符文本 */
  placeholder?: string;
  
  /** 是否禁用输入 */
  disabled?: boolean;
  
  /** 编辑器最大高度 */
  maxHeight?: number;
  
  /** 自定义 CSS 类名 */
  className?: string;
  
  /** 是否显示标签选择区域 */
  showTagSelection?: boolean;
  
  /** 标签选择区域的标题 */
  tagSelectionTitle?: string;
  
  /** 是否显示已选择标签的预览 */
  showSelectedTagsPreview?: boolean;
  
  /** 发送按钮回调 */
  onSend?: (content: { tags: AvailableTag[]; text: string; richText: Descendant[] }) => void;
  
  /** 是否显示发送按钮 */
  showSendButton?: boolean;
  
  /** 发送按钮文本 */
  sendButtonText?: string;
}

/**
 * 标签分组接口
 */
export interface TagGroup {
  [category: string]: AvailableTag[];
}

/**
 * ChatInput 组件的状态接口
 */
export interface ChatInputState {
  selectedTags: AvailableTag[];
  textValue: string;
  editorValue: Descendant[];
}

/**
 * 发送内容的接口
 */
export interface SendContent {
  tags: AvailableTag[];
  text: string;
  richText: Descendant[];
}
