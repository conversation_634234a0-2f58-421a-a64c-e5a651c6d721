import React, { useState } from 'react';
import { Descendant } from 'slate';
import { 
  Box, 
  Container, 
  Typography, 
  Paper, 
  Button, 
  Stack,
  Divider 
} from '@mui/material';
import RichTextInput, { AvailableTag } from './index';

// Extended set of demo tags
const demoTags: AvailableTag[] = [
  // People
  { id: 'user', label: 'User', color: '#2196f3', category: 'People' },
  { id: 'admin', label: 'Admin', color: '#3f51b5', category: 'People' },
  { id: 'customer', label: 'Customer', color: '#009688', category: 'People' },
  { id: 'team', label: 'Team', color: '#4caf50', category: 'People' },
  
  // Work
  { id: 'project', label: 'Project', color: '#4caf50', category: 'Work' },
  { id: 'task', label: 'Task', color: '#8bc34a', category: 'Work' },
  { id: 'feature', label: 'Feature', color: '#cddc39', category: 'Work' },
  { id: 'bug', label: 'Bug', color: '#f44336', category: 'Work' },
  
  // Priority
  { id: 'urgent', label: 'Urgent', color: '#f44336', category: 'Priority' },
  { id: 'high', label: 'High Priority', color: '#ff5722', category: 'Priority' },
  { id: 'medium', label: 'Medium Priority', color: '#ff9800', category: 'Priority' },
  { id: 'low', label: 'Low Priority', color: '#ffc107', category: 'Priority' },
  
  // Events
  { id: 'meeting', label: 'Meeting', color: '#ff9800', category: 'Events' },
  { id: 'deadline', label: 'Deadline', color: '#9c27b0', category: 'Events' },
  { id: 'launch', label: 'Launch', color: '#e91e63', category: 'Events' },
  { id: 'demo', label: 'Demo', color: '#673ab7', category: 'Events' },
  
  // Process
  { id: 'review', label: 'Review', color: '#607d8b', category: 'Process' },
  { id: 'testing', label: 'Testing', color: '#795548', category: 'Process' },
  { id: 'deployment', label: 'Deployment', color: '#9e9e9e', category: 'Process' },
  { id: 'documentation', label: 'Documentation', color: '#78909c', category: 'Process' },
];

const RichTextInputDemo: React.FC = () => {
  const [editorValue, setEditorValue] = useState<Descendant[]>([
    {
      type: 'paragraph',
      children: [{ text: 'Welcome to the Rich Text Input demo! Click on tags above to insert them into this text. You can type normally before and after tags.' }],
    },
  ]);

  const [secondEditorValue, setSecondEditorValue] = useState<Descendant[]>([
    {
      type: 'paragraph',
      children: [{ text: '' }],
    },
  ]);

  const handleClearFirst = () => {
    setEditorValue([
      {
        type: 'paragraph',
        children: [{ text: '' }],
      },
    ]);
  };

  const handleClearSecond = () => {
    setSecondEditorValue([
      {
        type: 'paragraph',
        children: [{ text: '' }],
      },
    ]);
  };

  const getTextContent = (nodes: Descendant[]): string => {
    return nodes
      .map(node => {
        if ('children' in node) {
          if (node.type === 'tag') {
            return `[${(node as any).label}]`;
          }
          return getTextContent(node.children);
        }
        return (node as any).text || '';
      })
      .join('');
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h3" component="h1" gutterBottom align="center">
        Rich Text Input with Slate.js
      </Typography>
      
      <Typography variant="body1" color="text.secondary" align="center" sx={{ mb: 4 }}>
        A React component that combines Slate.js editor with external tag selection
      </Typography>

      <Stack spacing={4}>
        {/* First Editor - Pre-filled with demo content */}
        <Paper elevation={2} sx={{ p: 3 }}>
          <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 2 }}>
            <Typography variant="h5" component="h2">
              Demo Editor #1
            </Typography>
            <Button variant="outlined" size="small" onClick={handleClearFirst}>
              Clear Content
            </Button>
          </Stack>
          
          <RichTextInput
            value={editorValue}
            onChange={setEditorValue}
            availableTags={demoTags}
            placeholder="Start typing and click tags to insert them..."
            maxHeight={300}
          />
          
          <Divider sx={{ my: 2 }} />
          
          <Typography variant="subtitle2" gutterBottom>
            Current Content (as text):
          </Typography>
          <Paper variant="outlined" sx={{ p: 2, backgroundColor: '#f5f5f5' }}>
            <Typography variant="body2" component="pre" sx={{ whiteSpace: 'pre-wrap', fontFamily: 'monospace' }}>
              {getTextContent(editorValue) || '(empty)'}
            </Typography>
          </Paper>
        </Paper>

        {/* Second Editor - Empty for testing */}
        <Paper elevation={2} sx={{ p: 3 }}>
          <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 2 }}>
            <Typography variant="h5" component="h2">
              Demo Editor #2 (Empty)
            </Typography>
            <Button variant="outlined" size="small" onClick={handleClearSecond}>
              Clear Content
            </Button>
          </Stack>
          
          <RichTextInput
            value={secondEditorValue}
            onChange={setSecondEditorValue}
            availableTags={demoTags.slice(0, 8)} // Subset of tags for variety
            placeholder="This editor starts empty. Try adding some tags and text..."
            maxHeight={200}
          />
          
          <Divider sx={{ my: 2 }} />
          
          <Typography variant="subtitle2" gutterBottom>
            Current Content (as text):
          </Typography>
          <Paper variant="outlined" sx={{ p: 2, backgroundColor: '#f5f5f5' }}>
            <Typography variant="body2" component="pre" sx={{ whiteSpace: 'pre-wrap', fontFamily: 'monospace' }}>
              {getTextContent(secondEditorValue) || '(empty)'}
            </Typography>
          </Paper>
        </Paper>

        {/* Usage Instructions */}
        <Paper elevation={1} sx={{ p: 3, backgroundColor: '#f8f9fa' }}>
          <Typography variant="h6" gutterBottom>
            How to Use:
          </Typography>
          <Stack spacing={1}>
            <Typography variant="body2">
              • Click on any tag in the "Available Tags" section to insert it at the current cursor position
            </Typography>
            <Typography variant="body2">
              • Tags are organized by categories (People, Work, Priority, Events, Process)
            </Typography>
            <Typography variant="body2">
              • You can type normally before and after tags
            </Typography>
            <Typography variant="body2">
              • Tags appear as styled chips within the text and maintain proper text flow
            </Typography>
            <Typography variant="body2">
              • The editor supports all standard text editing operations (copy, paste, undo, redo)
            </Typography>
            <Typography variant="body2">
              • Tags are treated as inline void elements and cannot be edited directly
            </Typography>
          </Stack>
        </Paper>

        {/* Features */}
        <Paper elevation={1} sx={{ p: 3, backgroundColor: '#e8f5e8' }}>
          <Typography variant="h6" gutterBottom>
            Key Features:
          </Typography>
          <Stack spacing={1}>
            <Typography variant="body2">
              ✅ External tag selection UI separate from the editor
            </Typography>
            <Typography variant="body2">
              ✅ Tags inserted at cursor position with proper text flow
            </Typography>
            <Typography variant="body2">
              ✅ Visual distinction between tags and regular text
            </Typography>
            <Typography variant="body2">
              ✅ TypeScript support with proper type definitions
            </Typography>
            <Typography variant="body2">
              ✅ Customizable tag categories and colors
            </Typography>
            <Typography variant="body2">
              ✅ Responsive design with Material-UI integration
            </Typography>
            <Typography variant="body2">
              ✅ Proper cursor positioning and text editing
            </Typography>
            <Typography variant="body2">
              ✅ Undo/Redo support via slate-history
            </Typography>
          </Stack>
        </Paper>
      </Stack>
    </Container>
  );
};

export default RichTextInputDemo;
