# Rich Text Input Component

A React component that provides a rich text editor using Slate.js with external tag selection functionality.

## Features

- **Slate.js Integration**: Built on top of the powerful Slate.js framework for rich text editing
- **External Tag Selection**: Tags are displayed outside the editor for easy selection
- **Visual Tag Distinction**: Tags appear as styled chips within the editor content
- **Proper Text Flow**: Normal text can be typed before and after tags with proper cursor positioning
- **TypeScript Support**: Fully typed with comprehensive TypeScript definitions
- **Material-UI Integration**: Uses Material-UI components for consistent styling
- **Categorized Tags**: Tags can be organized into categories for better organization
- **Customizable**: Supports custom tag colors, categories, and styling
- **Responsive Design**: Works well on both desktop and mobile devices

## Installation

The component requires the following dependencies (already included in this project):

```bash
npm install slate slate-react slate-history @mui/material @emotion/react @emotion/styled
```

## Basic Usage

```tsx
import React, { useState } from 'react';
import { Descendant } from 'slate';
import RichTextInput, { AvailableTag } from './components/RichTextInput';

const MyComponent = () => {
  const [value, setValue] = useState<Descendant[]>([
    {
      type: 'paragraph',
      children: [{ text: 'Start typing here...' }],
    },
  ]);

  const tags: AvailableTag[] = [
    { id: 'user', label: 'User', color: '#2196f3', category: 'People' },
    { id: 'urgent', label: 'Urgent', color: '#f44336', category: 'Priority' },
    { id: 'meeting', label: 'Meeting', color: '#ff9800', category: 'Events' },
  ];

  return (
    <RichTextInput
      value={value}
      onChange={setValue}
      availableTags={tags}
      placeholder="Type your message..."
      maxHeight={300}
    />
  );
};
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `value` | `Descendant[]` | `undefined` | The current value of the editor |
| `onChange` | `(value: Descendant[]) => void` | `undefined` | Callback fired when the editor value changes |
| `availableTags` | `AvailableTag[]` | Default tags | Array of tags available for insertion |
| `placeholder` | `string` | `"Type your message..."` | Placeholder text for the editor |
| `disabled` | `boolean` | `false` | Whether the editor is disabled |
| `maxHeight` | `number` | `200` | Maximum height of the editor in pixels |
| `className` | `string` | `""` | Additional CSS class name |

## AvailableTag Interface

```tsx
interface AvailableTag {
  id: string;        // Unique identifier for the tag
  label: string;     // Display text for the tag
  color?: string;    // Optional color for the tag (hex format)
  category?: string; // Optional category for grouping tags
}
```

## Advanced Usage

### Custom Tag Categories

```tsx
const categorizedTags: AvailableTag[] = [
  { id: 'john', label: 'John Doe', color: '#2196f3', category: 'People' },
  { id: 'jane', label: 'Jane Smith', color: '#4caf50', category: 'People' },
  { id: 'urgent', label: 'Urgent', color: '#f44336', category: 'Priority' },
  { id: 'low', label: 'Low Priority', color: '#ffc107', category: 'Priority' },
  { id: 'meeting', label: 'Meeting', color: '#ff9800', category: 'Events' },
  { id: 'deadline', label: 'Deadline', color: '#9c27b0', category: 'Events' },
];
```

### Extracting Text Content

```tsx
const getTextContent = (nodes: Descendant[]): string => {
  return nodes
    .map(node => {
      if ('children' in node) {
        if (node.type === 'tag') {
          return `[${(node as any).label}]`;
        }
        return getTextContent(node.children);
      }
      return (node as any).text || '';
    })
    .join('');
};

// Usage
const textContent = getTextContent(editorValue);
console.log(textContent); // "Hello [User] please review the [Urgent] task"
```

### Controlled Component

```tsx
const ControlledEditor = () => {
  const [value, setValue] = useState<Descendant[]>([
    {
      type: 'paragraph',
      children: [{ text: 'Initial content' }],
    },
  ]);

  const handleChange = (newValue: Descendant[]) => {
    setValue(newValue);
    // Perform additional logic like saving to backend
    console.log('Editor content changed:', newValue);
  };

  return (
    <RichTextInput
      value={value}
      onChange={handleChange}
      availableTags={myTags}
    />
  );
};
```

## Styling

The component includes default CSS styling, but you can customize it by:

1. **Overriding CSS classes**: The component uses the class name `rich-text-input`
2. **Using the className prop**: Pass additional CSS classes
3. **Material-UI theme**: The component respects your Material-UI theme

```css
.rich-text-input {
  /* Your custom styles */
}

.rich-text-input .tag-element {
  /* Custom tag styling */
}
```

## Browser Support

- Chrome 60+
- Firefox 55+
- Safari 11+
- Edge 79+

## Demo

See `RichTextInputDemo.tsx` for a comprehensive example with multiple editors and various tag configurations.
