import React, { useState } from 'react';
import { Descendant } from 'slate';
import { Box, Button, Typography, Paper, Alert, Stack } from '@mui/material';
import RichTextInput, { AvailableTag } from './index';

// Simple example with minimal configuration
const SimpleExample: React.FC = () => {
  const [value, setValue] = useState<Descendant[]>([
    {
      type: 'paragraph',
      children: [{ text: '' }],
    },
  ]);

  const [submitted, setSubmitted] = useState(false);

  // Simple set of tags
  const simpleTags: AvailableTag[] = [
    { id: 'user', label: '@User', color: '#2196f3' },
    { id: 'urgent', label: 'Urgent', color: '#f44336' },
    { id: 'task', label: 'Task', color: '#4caf50' },
    { id: 'meeting', label: 'Meeting', color: '#ff9800' },
  ];

  const handleSubmit = () => {
    console.log('Submitted content:', value);
    setSubmitted(true);
    setTimeout(() => setSubmitted(false), 3000);
  };

  const handleClear = () => {
    setValue([
      {
        type: 'paragraph',
        children: [{ text: '' }],
      },
    ]);
  };

  // Extract plain text for display
  const getPlainText = (nodes: Descendant[]): string => {
    return nodes
      .map((node) => {
        if ('children' in node) {
          if (node.type === 'tag') {
            return `[${(node as any).label}]`;
          }
          return getPlainText(node.children);
        }
        return (node as any).text || '';
      })
      .join('');
  };

  const plainText = getPlainText(value);
  const hasContent = plainText.trim().length > 0;

  return (
    <Box sx={{ maxWidth: 600, mx: 'auto', p: 3 }}>
      <Typography variant="h5" gutterBottom>
        Simple Rich Text Input Example
      </Typography>

      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        This is a minimal example showing how to use the RichTextInput component in a form.
      </Typography>

      {submitted && (
        <Alert severity="success" sx={{ mb: 2 }}>
          Content submitted successfully!
        </Alert>
      )}

      <Paper elevation={1} sx={{ p: 2, mb: 2 }}>
        <RichTextInput value={value} onChange={setValue} placeholder="Type your message and click tags to insert them..." maxHeight={150} />
      </Paper>

      <Stack direction="row" spacing={2} sx={{ mb: 2 }}>
        <Button variant="contained" onClick={handleSubmit} disabled={!hasContent}>
          Submit
        </Button>
        <Button variant="outlined" onClick={handleClear} disabled={!hasContent}>
          Clear
        </Button>
      </Stack>

      {hasContent && (
        <Paper variant="outlined" sx={{ p: 2, backgroundColor: '#f5f5f5' }}>
          <Typography variant="subtitle2" gutterBottom>
            Preview (as plain text):
          </Typography>
          <Typography variant="body2" component="pre" sx={{ whiteSpace: 'pre-wrap' }}>
            {plainText}
          </Typography>
        </Paper>
      )}
    </Box>
  );
};

export default SimpleExample;

