.rich-text-input {
  width: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
}

.rich-text-input .tag-element {
  user-select: none;
  cursor: default;
}

/* Ensure proper spacing around tags */
.rich-text-input [data-slate-editor] {
  line-height: 1.6;
  white-space: pre-wrap; /* Preserve line breaks and wrap text */
  word-wrap: break-word; /* Break long words */
  overflow-wrap: break-word; /* Modern browsers */
}

.rich-text-input [data-slate-editor] > div {
  margin: 0;
  padding: 0;
}

/* Custom focus styles for the editor */
.rich-text-input [data-slate-editor]:focus {
  outline: none;
}

/* Ensure tags don't break text flow */
.rich-text-input .tag-element + .tag-element {
  margin-left: 4px;
}

/* Placeholder styling */
.rich-text-input [data-slate-placeholder] {
  color: #9e9e9e;
  opacity: 1;
  pointer-events: none;
  user-select: none;
  font-style: italic;
}

/* Ensure proper cursor positioning around tags */
.rich-text-input [data-slate-void] {
  position: relative;
}

.rich-text-input [data-slate-void]::before {
  content: '';
  position: absolute;
  left: -1px;
  top: 0;
  bottom: 0;
  width: 1px;
  background: transparent;
}

.rich-text-input [data-slate-void]::after {
  content: '';
  position: absolute;
  right: -1px;
  top: 0;
  bottom: 0;
  width: 1px;
  background: transparent;
}

/* Responsive design */
@media (max-width: 768px) {
  .rich-text-input {
    font-size: 16px; /* Prevent zoom on iOS */
  }
}

/* Animation for tag insertion */
.rich-text-input .tag-element {
  animation: tagInsert 0.2s ease-out;
}

@keyframes tagInsert {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Scrollbar styling for the editor */
.rich-text-input .MuiBox-root::-webkit-scrollbar {
  width: 6px;
}

.rich-text-input .MuiBox-root::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.rich-text-input .MuiBox-root::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.rich-text-input .MuiBox-root::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

