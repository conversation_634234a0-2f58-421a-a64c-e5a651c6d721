import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Container, Typography, Button, Stack, Paper } from '@mui/material';

const Home: React.FC = () => {
  return (
    <Container maxWidth="md" sx={{ py: 4 }}>
      <Paper elevation={2} sx={{ p: 4, textAlign: 'center' }}>
        <Typography variant="h3" component="h1" gutterBottom>
          React Vite Template
        </Typography>

        <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
          Welcome to the React + Vite template with TypeScript and Material-UI
        </Typography>

        <Stack spacing={2} direction="row" justifyContent="center">
          <Button component={Link} to="/rich-text-input-example" variant="contained" size="large">
            Rich Text Input Demo
          </Button>

          <Button component={Link} to="/login" variant="outlined" size="large">
            Login Page
          </Button>
        </Stack>
      </Paper>
    </Container>
  );
};

export default Home;

