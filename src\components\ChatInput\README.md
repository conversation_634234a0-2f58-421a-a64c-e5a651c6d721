# ChatInput Component

ChatInput 是对 RichTextInput 组件的重构版本，将标签选择功能完全外部化，提供更灵活的使用方式。

## 主要特性

- **外部化标签选择**：标签选择逻辑完全移到组件外部，可以独立管理
- **分离的输入参数**：标签和文本内容通过独立的参数传入
- **灵活的状态管理**：支持外部状态管理和内部状态管理两种模式
- **完整的 TypeScript 支持**：提供完整的类型定义
- **可定制的 UI**：支持隐藏标签选择区域、自定义按钮等
- **发送功能集成**：内置发送按钮和回调处理

## 安装和导入

```tsx
import { ChatInput } from './components/ChatInput';
import type { ChatInputProps, AvailableTag } from './components/ChatInput/exports';
```

## 基本用法

### 1. 外部状态管理模式

```tsx
import React, { useState } from 'react';
import { ChatInput, AvailableTag } from './components/ChatInput';

const MyComponent = () => {
  const [selectedTags, setSelectedTags] = useState<AvailableTag[]>([]);
  const [textValue, setTextValue] = useState<string>('');

  const availableTags: AvailableTag[] = [
    { id: 'urgent', label: 'Urgent', color: '#f44336', category: 'Priority' },
    { id: 'project', label: 'Project', color: '#4caf50', category: 'Work' },
  ];

  return (
    <ChatInput
      selectedTags={selectedTags}
      textValue={textValue}
      onTagsChange={setSelectedTags}
      onTextChange={setTextValue}
      availableTags={availableTags}
      showSendButton={true}
      onSend={(content) => {
        console.log('Sent:', content);
        // 清空输入
        setSelectedTags([]);
        setTextValue('');
      }}
    />
  );
};
```

### 2. 内部状态管理模式

```tsx
const SimpleComponent = () => {
  return (
    <ChatInput
      availableTags={myTags}
      placeholder="Type your message..."
      showSendButton={true}
      onSend={(content) => {
        console.log('Content:', content);
      }}
    />
  );
};
```

### 3. 纯文本输入模式

```tsx
const TextOnlyComponent = () => {
  return (
    <ChatInput
      showTagSelection={false}
      placeholder="Simple text input..."
      onTextChange={(text) => console.log('Text:', text)}
    />
  );
};
```

## Props 接口

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `selectedTags` | `AvailableTag[]` | `[]` | 当前选中的标签数组 |
| `textValue` | `string` | `''` | 当前的文本内容 |
| `onTagsChange` | `(tags: AvailableTag[]) => void` | - | 标签选择变化回调 |
| `onTextChange` | `(text: string) => void` | - | 文本内容变化回调 |
| `availableTags` | `AvailableTag[]` | 默认标签 | 可用的标签列表 |
| `showTagSelection` | `boolean` | `true` | 是否显示标签选择区域 |
| `showSendButton` | `boolean` | `false` | 是否显示发送按钮 |
| `onSend` | `(content: SendContent) => void` | - | 发送按钮回调 |
| `placeholder` | `string` | `'Type your message...'` | 输入框占位符 |
| `disabled` | `boolean` | `false` | 是否禁用输入 |
| `maxHeight` | `number` | `200` | 编辑器最大高度 |

## 类型定义

### AvailableTag

```tsx
interface AvailableTag {
  id: string;
  label: string;
  color?: string;
  category?: string;
}
```

### SendContent

```tsx
interface SendContent {
  tags: AvailableTag[];
  text: string;
  richText: Descendant[];
}
```

## 与 RichTextInput 的区别

| 特性 | RichTextInput | ChatInput |
|------|---------------|-----------|
| 标签选择 | 内置在组件内部 | 完全外部化 |
| 状态管理 | 主要依赖外部 | 支持内外部两种模式 |
| 文本输入 | 通过 Slate 值管理 | 独立的 textValue 参数 |
| UI 布局 | 固定布局 | 可配置显示/隐藏区域 |
| 发送功能 | 无内置支持 | 内置发送按钮和回调 |

## 使用场景

1. **聊天应用**：需要标签和文本分离管理的聊天输入框
2. **表单输入**：需要预填充标签和文本的表单字段
3. **评论系统**：支持标签分类的评论输入
4. **任务管理**：带有优先级和分类标签的任务创建

## 示例

查看 `ChatInputExample.tsx` 文件获取完整的使用示例。
