import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Stack,
  Alert,
  Button,
  Divider,
} from '@mui/material';
import { Descendant } from 'slate';
import { ChatInput } from './index';
import { AvailableTag, SendContent } from './exports';

// 示例可用标签
const exampleTags: AvailableTag[] = [
  { id: 'user', label: 'User', color: '#2196f3', category: 'People' },
  { id: 'admin', label: 'Admin', color: '#1976d2', category: 'People' },
  { id: 'project', label: 'Project', color: '#4caf50', category: 'Work' },
  { id: 'task', label: 'Task', color: '#388e3c', category: 'Work' },
  { id: 'urgent', label: 'Urgent', color: '#f44336', category: 'Priority' },
  { id: 'normal', label: 'Normal', color: '#ff9800', category: 'Priority' },
  { id: 'meeting', label: 'Meeting', color: '#9c27b0', category: 'Events' },
  { id: 'deadline', label: 'Deadline', color: '#673ab7', category: 'Events' },
  { id: 'review', label: 'Review', color: '#607d8b', category: 'Process' },
  { id: 'approval', label: 'Approval', color: '#795548', category: 'Process' },
];

export const ChatInputExample: React.FC = () => {
  // 状态管理
  const [selectedTags, setSelectedTags] = useState<AvailableTag[]>([]);
  const [textValue, setTextValue] = useState<string>('');
  const [editorValue, setEditorValue] = useState<Descendant[]>();
  const [lastSentContent, setLastSentContent] = useState<SendContent | null>(null);

  // 处理发送
  const handleSend = (content: SendContent) => {
    setLastSentContent(content);
    console.log('Sent content:', content);
    
    // 清空输入
    setSelectedTags([]);
    setTextValue('');
    setEditorValue([{ type: 'paragraph', children: [{ text: '' }] }]);
  };

  // 重置示例
  const resetExample = () => {
    setSelectedTags([]);
    setTextValue('');
    setEditorValue(undefined);
    setLastSentContent(null);
  };

  // 设置示例数据
  const setExampleData = () => {
    setSelectedTags([
      exampleTags.find(tag => tag.id === 'urgent')!,
      exampleTags.find(tag => tag.id === 'project')!,
    ]);
    setTextValue('This is an example message with pre-filled content.');
  };

  return (
    <Box sx={{ maxWidth: 800, mx: 'auto', p: 3 }}>
      <Typography variant="h4" gutterBottom>
        ChatInput Component Example
      </Typography>

      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        This example demonstrates the ChatInput component, which is a refactored version of RichTextInput
        with externalized tag selection functionality.
      </Typography>

      {/* 控制按钮 */}
      <Stack direction="row" spacing={2} sx={{ mb: 3 }}>
        <Button variant="outlined" onClick={setExampleData}>
          Set Example Data
        </Button>
        <Button variant="outlined" onClick={resetExample}>
          Reset
        </Button>
      </Stack>

      {/* 显示最后发送的内容 */}
      {lastSentContent && (
        <Alert severity="success" sx={{ mb: 3 }}>
          <Typography variant="subtitle2" gutterBottom>
            Content sent successfully!
          </Typography>
          <Typography variant="body2">
            <strong>Tags:</strong> {lastSentContent.tags.map(tag => tag.label).join(', ') || 'None'}
          </Typography>
          <Typography variant="body2">
            <strong>Text:</strong> {lastSentContent.text || 'Empty'}
          </Typography>
        </Alert>
      )}

      {/* ChatInput 组件示例 */}
      <Paper elevation={2} sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>
          Basic ChatInput with External State Management
        </Typography>
        
        <ChatInput
          selectedTags={selectedTags}
          textValue={textValue}
          onTagsChange={setSelectedTags}
          onTextChange={setTextValue}
          value={editorValue}
          onChange={setEditorValue}
          availableTags={exampleTags}
          placeholder="Type your message here..."
          showSendButton={true}
          onSend={handleSend}
          maxHeight={150}
        />
      </Paper>

      <Divider sx={{ my: 4 }} />

      {/* 独立的 ChatInput 示例 */}
      <Paper elevation={2} sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>
          Self-Managed ChatInput (Internal State)
        </Typography>
        
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          This example uses internal state management and doesn't require external state handling.
        </Typography>
        
        <ChatInput
          availableTags={exampleTags}
          placeholder="This ChatInput manages its own state..."
          showSendButton={true}
          onSend={(content) => {
            console.log('Self-managed content:', content);
            alert(`Sent: ${content.tags.length} tags, "${content.text}"`);
          }}
          maxHeight={150}
          tagSelectionTitle="Choose Tags"
          sendButtonText="Submit"
        />
      </Paper>

      <Divider sx={{ my: 4 }} />

      {/* 无标签选择的示例 */}
      <Paper elevation={2} sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>
          Text-Only ChatInput (No Tag Selection)
        </Typography>
        
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          This example hides the tag selection area and works as a simple rich text input.
        </Typography>
        
        <ChatInput
          availableTags={exampleTags}
          placeholder="Simple text input without tag selection..."
          showTagSelection={false}
          showSendButton={true}
          onSend={(content) => {
            console.log('Text-only content:', content);
            alert(`Sent text: "${content.text}"`);
          }}
          maxHeight={100}
        />
      </Paper>

      {/* 当前状态显示 */}
      <Paper elevation={1} sx={{ p: 2, mt: 3, backgroundColor: '#f5f5f5' }}>
        <Typography variant="subtitle2" gutterBottom>
          Current State (External Management Example):
        </Typography>
        <Typography variant="body2">
          <strong>Selected Tags:</strong> {selectedTags.map(tag => tag.label).join(', ') || 'None'}
        </Typography>
        <Typography variant="body2">
          <strong>Text Value:</strong> "{textValue || 'Empty'}"
        </Typography>
      </Paper>
    </Box>
  );
};

export default ChatInputExample;
