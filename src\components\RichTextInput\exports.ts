// Main component exports
export { default as RichTextInput } from './index';
export type { 
  RichTextInputProps, 
  AvailableTag, 
  TagElement, 
  ParagraphElement, 
  TextElement, 
  CustomElement, 
  CustomText 
} from './index';

// Demo components
export { default as RichTextInputDemo } from './RichTextInputDemo';
export { default as SimpleExample } from './SimpleExample';

// Utility functions for working with Slate.js content
import { Descendant } from 'slate';

/**
 * Extracts plain text content from Slate.js nodes, replacing tags with their labels
 */
export const extractTextContent = (nodes: Descendant[]): string => {
  return nodes
    .map(node => {
      if ('children' in node) {
        if (node.type === 'tag') {
          return `[${(node as any).label}]`;
        }
        return extractTextContent(node.children);
      }
      return (node as any).text || '';
    })
    .join('');
};

/**
 * Extracts only the plain text without tag labels
 */
export const extractPlainText = (nodes: Descendant[]): string => {
  return nodes
    .map(node => {
      if ('children' in node) {
        if (node.type === 'tag') {
          return ''; // Skip tags entirely
        }
        return extractPlainText(node.children);
      }
      return (node as any).text || '';
    })
    .join('');
};

/**
 * Extracts all tags from the content
 */
export const extractTags = (nodes: Descendant[]): Array<{ id: string; label: string }> => {
  const tags: Array<{ id: string; label: string }> = [];
  
  const traverse = (nodes: Descendant[]) => {
    nodes.forEach(node => {
      if ('children' in node) {
        if (node.type === 'tag') {
          const tagNode = node as any;
          tags.push({
            id: tagNode.tagId,
            label: tagNode.label,
          });
        } else {
          traverse(node.children);
        }
      }
    });
  };
  
  traverse(nodes);
  return tags;
};

/**
 * Creates an empty editor value
 */
export const createEmptyValue = (): Descendant[] => [
  {
    type: 'paragraph',
    children: [{ text: '' }],
  },
];

/**
 * Creates an editor value with initial text
 */
export const createValueWithText = (text: string): Descendant[] => [
  {
    type: 'paragraph',
    children: [{ text }],
  },
];

/**
 * Checks if the editor content is empty
 */
export const isContentEmpty = (nodes: Descendant[]): boolean => {
  const textContent = extractTextContent(nodes).trim();
  return textContent.length === 0;
};

/**
 * Default tag configurations for common use cases
 */
export const defaultTagSets = {
  people: [
    { id: 'user', label: '@User', color: '#2196f3', category: 'People' },
    { id: 'admin', label: '@Admin', color: '#3f51b5', category: 'People' },
    { id: 'team', label: '@Team', color: '#4caf50', category: 'People' },
  ],
  
  priority: [
    { id: 'urgent', label: 'Urgent', color: '#f44336', category: 'Priority' },
    { id: 'high', label: 'High', color: '#ff5722', category: 'Priority' },
    { id: 'medium', label: 'Medium', color: '#ff9800', category: 'Priority' },
    { id: 'low', label: 'Low', color: '#ffc107', category: 'Priority' },
  ],
  
  status: [
    { id: 'todo', label: 'To Do', color: '#9e9e9e', category: 'Status' },
    { id: 'progress', label: 'In Progress', color: '#2196f3', category: 'Status' },
    { id: 'review', label: 'Review', color: '#ff9800', category: 'Status' },
    { id: 'done', label: 'Done', color: '#4caf50', category: 'Status' },
  ],
  
  events: [
    { id: 'meeting', label: 'Meeting', color: '#ff9800', category: 'Events' },
    { id: 'deadline', label: 'Deadline', color: '#9c27b0', category: 'Events' },
    { id: 'launch', label: 'Launch', color: '#e91e63', category: 'Events' },
    { id: 'demo', label: 'Demo', color: '#673ab7', category: 'Events' },
  ],
};
